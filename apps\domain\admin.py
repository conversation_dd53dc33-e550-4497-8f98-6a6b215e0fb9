"""
Django Admin Interface for Hospital Management System
Provides administrative interface for managing all domain entities.
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models.user import NguoiDung
from .models.department import <PERSON><PERSON><PERSON>Phong
from .models.patient import <PERSON>h<PERSON>han
from .models.medical_staff import NhanVienYTe
from .models.room import PhongBenh
from .models.appointment import LichHen
from .models.medication import Thuoc
from .models.prescription import DonThuoc, ChiTietDonThuoc
from .models.bill import HoaDon


# Custom User Admin
# @admin.register(NguoiDung)  # Temporarily disabled for setup
class NguoiDungAdmin(BaseUserAdmin):
    """Admin interface for User model."""
    
    list_display = ('username', 'ho_ten', 'email', 'vai_tro', 'trang_thai', 'is_active', 'date_joined')
    list_filter = ('vai_tro', 'trang_thai', 'is_active', 'gioi_tinh', 'date_joined')
    search_fields = ('username', 'ho_ten', 'email', 'so_dien_thoai')
    ordering = ('username',)
    
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('Thông tin cá nhân', {
            'fields': ('ho_ten', 'email', 'so_dien_thoai', 'dia_chi', 'ngay_sinh', 'gioi_tinh', 'avatar')
        }),
        ('Phân quyền', {
            'fields': ('vai_tro', 'trang_thai', 'is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')
        }),
        ('Thời gian', {
            'fields': ('last_login', 'date_joined')
        }),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'ho_ten', 'email', 'password1', 'password2', 'vai_tro'),
        }),
    )
    
    readonly_fields = ('date_joined', 'last_login')


# Department Admin
# @admin.register(KhoaPhong)  # Temporarily disabled for setup
class KhoaPhongAdmin(admin.ModelAdmin):
    """Admin interface for Department model."""
    
    list_display = ('ten_khoa_phong', 'truong_khoa', 'tang', 'so_giuong', 'so_phong', 'trang_thai')
    list_filter = ('trang_thai', 'tang')
    search_fields = ('ten_khoa_phong', 'truong_khoa', 'mo_ta')
    ordering = ('ten_khoa_phong',)
    
    fieldsets = (
        ('Thông tin cơ bản', {
            'fields': ('ten_khoa_phong', 'mo_ta', 'truong_khoa')
        }),
        ('Vị trí', {
            'fields': ('vi_tri', 'tang')
        }),
        ('Liên hệ', {
            'fields': ('dien_thoai', 'email')
        }),
        ('Cơ sở vật chất', {
            'fields': ('so_giuong', 'so_phong')
        }),
        ('Thời gian hoạt động', {
            'fields': ('gio_mo_cua', 'gio_dong_cua')
        }),
        ('Trạng thái', {
            'fields': ('trang_thai', 'ghi_chu')
        }),
    )


# Patient Admin
# @admin.register(BenhNhan)  # Temporarily disabled for setup
class BenhNhanAdmin(admin.ModelAdmin):
    """Admin interface for Patient model."""
    
    list_display = ('ma_benh_nhan', 'ho_ten', 'ngay_sinh', 'gioi_tinh', 'so_dien_thoai', 'trang_thai')
    list_filter = ('trang_thai', 'gioi_tinh', 'loai_bao_hiem', 'dan_toc')
    search_fields = ('ma_benh_nhan', 'ho_ten', 'so_dien_thoai', 'so_cccd', 'so_bao_hiem')
    ordering = ('-created_at',)
    
    fieldsets = (
        ('Thông tin cơ bản', {
            'fields': ('ma_benh_nhan', 'ho_ten', 'ngay_sinh', 'gioi_tinh')
        }),
        ('Thông tin liên hệ', {
            'fields': ('so_dien_thoai', 'email', 'dia_chi')
        }),
        ('Thông tin cá nhân', {
            'fields': ('so_cccd', 'nghe_nghiep', 'dan_toc', 'ton_giao')
        }),
        ('Thông tin bảo hiểm', {
            'fields': ('so_bao_hiem', 'loai_bao_hiem')
        }),
        ('Người liên hệ', {
            'fields': ('nguoi_lien_he', 'sdt_nguoi_lien_he')
        }),
        ('Thông tin y tế', {
            'fields': ('tien_su_benh', 'di_ung')
        }),
        ('Trạng thái', {
            'fields': ('trang_thai', 'ghi_chu')
        }),
    )
    
    readonly_fields = ('ma_benh_nhan',)


# Medical Staff Admin
@admin.register(NhanVienYTe)
class NhanVienYTeAdmin(admin.ModelAdmin):
    """Admin interface for Medical Staff model."""
    
    list_display = ('ma_nhan_vien', 'ho_ten', 'chuc_vu', 'loai_nhan_vien', 'khoa_phong', 'trang_thai')
    list_filter = ('loai_nhan_vien', 'chuc_vu', 'khoa_phong', 'trang_thai', 'gioi_tinh')
    search_fields = ('ma_nhan_vien', 'ho_ten', 'so_dien_thoai', 'email', 'chuyen_khoa')
    ordering = ('ho_ten',)
    
    fieldsets = (
        ('Thông tin cơ bản', {
            'fields': ('ma_nhan_vien', 'ho_ten', 'ngay_sinh', 'gioi_tinh', 'so_cccd')
        }),
        ('Thông tin nghề nghiệp', {
            'fields': ('chuc_vu', 'loai_nhan_vien', 'khoa_phong', 'trinh_do', 'chuyen_khoa', 'kinh_nghiem')
        }),
        ('Thông tin liên hệ', {
            'fields': ('so_dien_thoai', 'email', 'dia_chi')
        }),
        ('Giấy phép hành nghề', {
            'fields': ('so_giay_phep', 'ngay_cap_giay_phep', 'noi_cap_giay_phep')
        }),
        ('Thông tin làm việc', {
            'fields': ('ngay_bat_dau', 'luong_co_ban')
        }),
        ('Trạng thái', {
            'fields': ('trang_thai', 'ghi_chu')
        }),
    )
    
    readonly_fields = ('ma_nhan_vien',)


# Room Admin
@admin.register(PhongBenh)
class PhongBenhAdmin(admin.ModelAdmin):
    """Admin interface for Room model."""
    
    list_display = ('ma_phong', 'ten_phong', 'loai_phong', 'khoa_phong', 'tang', 'so_giuong', 'so_giuong_trong', 'trang_thai')
    list_filter = ('loai_phong', 'khoa_phong', 'trang_thai', 'tang')
    search_fields = ('ma_phong', 'ten_phong')
    ordering = ('ma_phong',)
    
    fieldsets = (
        ('Thông tin cơ bản', {
            'fields': ('ma_phong', 'ten_phong', 'loai_phong', 'khoa_phong')
        }),
        ('Vị trí', {
            'fields': ('tang',)
        }),
        ('Cơ sở vật chất', {
            'fields': ('so_giuong', 'so_giuong_trong', 'thiet_bi')
        }),
        ('Giá cả', {
            'fields': ('gia_phong',)
        }),
        ('Trạng thái', {
            'fields': ('trang_thai', 'ghi_chu')
        }),
    )
    
    readonly_fields = ('ma_phong',)


# Appointment Admin
@admin.register(LichHen)
class LichHenAdmin(admin.ModelAdmin):
    """Admin interface for Appointment model."""
    
    list_display = ('ma_lich_hen', 'benh_nhan', 'bac_si', 'ngay_gio_hen', 'loai_kham', 'trang_thai')
    list_filter = ('trang_thai', 'loai_kham', 'bac_si__khoa_phong', 'ngay_gio_hen')
    search_fields = ('ma_lich_hen', 'benh_nhan__ho_ten', 'bac_si__ho_ten', 'ly_do_kham')
    ordering = ('-ngay_gio_hen',)
    
    fieldsets = (
        ('Thông tin cơ bản', {
            'fields': ('ma_lich_hen', 'benh_nhan', 'bac_si')
        }),
        ('Lịch hẹn', {
            'fields': ('ngay_gio_hen', 'loai_kham', 'ly_do_kham')
        }),
        ('Ghi chú', {
            'fields': ('ghi_chu_benh_nhan', 'ghi_chu_bac_si')
        }),
        ('Kết quả', {
            'fields': ('ket_qua_kham',)
        }),
        ('Trạng thái', {
            'fields': ('trang_thai',)
        }),
    )
    
    readonly_fields = ('ma_lich_hen',)


# Medication Admin
@admin.register(Thuoc)
class ThuocAdmin(admin.ModelAdmin):
    """Admin interface for Medication model."""
    
    list_display = ('ma_thuoc', 'ten_thuoc', 'nong_do', 'don_vi_tinh', 'gia_ban', 'so_luong_ton_kho', 'ngay_het_han', 'trang_thai')
    list_filter = ('trang_thai', 'dang_bao_che', 'don_vi_tinh', 'hang_san_xuat')
    search_fields = ('ma_thuoc', 'ten_thuoc', 'thanh_phan', 'hang_san_xuat', 'so_dang_ky')
    ordering = ('ten_thuoc',)
    
    fieldsets = (
        ('Thông tin cơ bản', {
            'fields': ('ma_thuoc', 'ten_thuoc', 'thanh_phan', 'nong_do')
        }),
        ('Dạng bào chế', {
            'fields': ('dang_bao_che', 'dong_goi', 'don_vi_tinh')
        }),
        ('Nhà sản xuất', {
            'fields': ('hang_san_xuat', 'nuoc_san_xuat', 'so_dang_ky')
        }),
        ('Lô sản xuất', {
            'fields': ('so_lo', 'ngay_san_xuat', 'ngay_het_han')
        }),
        ('Giá cả', {
            'fields': ('gia_nhap', 'gia_ban')
        }),
        ('Kho', {
            'fields': ('so_luong_ton_kho', 'muc_canh_bao_ton_kho', 'vi_tri_kho')
        }),
        ('Trạng thái', {
            'fields': ('trang_thai', 'ghi_chu')
        }),
    )
    
    readonly_fields = ('ma_thuoc',)


# Prescription Detail Inline
class ChiTietDonThuocInline(admin.TabularInline):
    """Inline admin for prescription details."""
    
    model = ChiTietDonThuoc
    extra = 1
    fields = ('thuoc', 'so_luong', 'lieu_dung', 'tan_suat', 'thoi_gian_dung', 'don_gia', 'thanh_tien', 'da_cap')
    readonly_fields = ('thanh_tien',)


# Prescription Admin
@admin.register(DonThuoc)
class DonThuocAdmin(admin.ModelAdmin):
    """Admin interface for Prescription model."""
    
    list_display = ('ma_don_thuoc', 'benh_nhan', 'bac_si', 'loai_don_thuoc', 'trang_thai', 'ngay_ke_don', 'tong_tien')
    list_filter = ('trang_thai', 'loai_don_thuoc', 'bac_si__khoa_phong', 'ngay_ke_don')
    search_fields = ('ma_don_thuoc', 'benh_nhan__ho_ten', 'bac_si__ho_ten', 'chan_doan')
    ordering = ('-ngay_ke_don',)
    inlines = [ChiTietDonThuocInline]
    
    fieldsets = (
        ('Thông tin cơ bản', {
            'fields': ('ma_don_thuoc', 'benh_nhan', 'bac_si', 'duoc_si')
        }),
        ('Thông tin đơn thuốc', {
            'fields': ('loai_don_thuoc', 'chan_doan', 'loi_dan_bac_si')
        }),
        ('Thời gian', {
            'fields': ('ngay_ke_don', 'ngay_cap_thuoc', 'ngay_het_han')
        }),
        ('Tài chính', {
            'fields': ('tong_tien',)
        }),
        ('Trạng thái', {
            'fields': ('trang_thai', 'ghi_chu')
        }),
    )
    
    readonly_fields = ('ma_don_thuoc', 'tong_tien')


# Bill Admin
@admin.register(HoaDon)
class HoaDonAdmin(admin.ModelAdmin):
    """Admin interface for Bill model."""
    
    list_display = ('ma_hoa_don', 'benh_nhan', 'tong_tien', 'da_thanh_toan', 'con_no', 'trang_thai', 'ngay_tao')
    list_filter = ('trang_thai', 'phuong_thuc_thanh_toan', 'ngay_tao')
    search_fields = ('ma_hoa_don', 'benh_nhan__ho_ten', 'mo_ta')
    ordering = ('-ngay_tao',)
    
    fieldsets = (
        ('Thông tin cơ bản', {
            'fields': ('ma_hoa_don', 'benh_nhan', 'lich_hen', 'don_thuoc')
        }),
        ('Mô tả dịch vụ', {
            'fields': ('mo_ta',)
        }),
        ('Chi phí', {
            'fields': ('phi_kham', 'phi_thuoc', 'phi_dich_vu', 'tong_tien')
        }),
        ('Thanh toán', {
            'fields': ('da_thanh_toan', 'con_no', 'bao_hiem_chi_tra', 'phuong_thuc_thanh_toan')
        }),
        ('Thời gian', {
            'fields': ('ngay_tao', 'ngay_dao_han', 'ngay_thanh_toan')
        }),
        ('Trạng thái', {
            'fields': ('trang_thai', 'ghi_chu')
        }),
    )
    
    readonly_fields = ('ma_hoa_don', 'tong_tien', 'con_no')


# Register additional configurations
admin.site.site_header = "Hệ thống Quản lý Bệnh viện"
admin.site.site_title = "Hospital Management"
admin.site.index_title = "Bảng điều khiển quản trị"
