"""
Room Domain Model
Defines the Room entity for hospital room management.
"""
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.domain.shared.base_entity import BaseEntity
from apps.domain.models.department import KhoaPhong
from decimal import Decimal


class PhongBenh(models.Model, BaseEntity):
    """
    Room model for hospital management system.
    Represents hospital rooms for patient care.
    """
    
    LOAI_PHONG_CHOICES = [
        ('Thuong', 'Phòng thường'),
        ('VIP', 'Phòng VIP'),
        ('ICU', 'Phòng hồi sức'),
        ('Phau_Thuat', 'Phòng phẫu thuật'),
        ('Kham', 'Phòng khám'),
        ('Cap_Cuu', '<PERSON>òng cấp cứu'),
        ('<PERSON><PERSON>_Nhan_Ngoai_Tru', 'Phòng bệnh nhân ngoại trú'),
    ]
    
    TRANG_THAI_CHOICES = [
        ('Available', '<PERSON>ó sẵn'),
        ('Occupied', '<PERSON>ang sử dụng'),
        ('Maintenance', '<PERSON><PERSON> bảo trì'),
        ('Cleaning', '<PERSON>ang dọn dẹp'),
        ('Reserved', '<PERSON><PERSON> đặt trước'),
        ('Out_Of_Order', 'Hỏng hóc'),
    ]
    
    # Primary Key
    PhongID = models.AutoField(primary_key=True)
    
    # Foreign Keys
    khoa_phong = models.ForeignKey(
        KhoaPhong,
        on_delete=models.PROTECT,
        related_name='phong_benh',
        verbose_name='Khoa phòng',
        help_text='Khoa phòng quản lý'
    )
    
    # Basic Information
    so_phong = models.CharField(
        'Số phòng',
        max_length=20,
        unique=True,
        help_text='Số phòng duy nhất'
    )
    
    ten_phong = models.CharField(
        'Tên phòng',
        max_length=100,
        blank=True,
        null=True,
        help_text='Tên mô tả của phòng'
    )
    
    loai_phong = models.CharField(
        'Loại phòng',
        max_length=30,
        choices=LOAI_PHONG_CHOICES,
        help_text='Loại phòng bệnh'
    )
    
    # Location
    tang = models.IntegerField(
        'Tầng',
        help_text='Tầng của phòng'
    )
    
    toa_nha = models.CharField(
        'Tòa nhà',
        max_length=50,
        default='Tòa A',
        help_text='Tòa nhà chứa phòng'
    )
    
    # Capacity and Equipment
    so_giuong = models.IntegerField(
        'Số giường',
        default=1,
        help_text='Số lượng giường trong phòng'
    )
    
    dien_tich = models.DecimalField(
        'Diện tích',
        max_digits=8,
        decimal_places=2,
        help_text='Diện tích phòng (m²)'
    )
    
    co_dieu_hoa = models.BooleanField(
        'Có điều hòa',
        default=True,
        help_text='Phòng có điều hòa không khí'
    )
    
    co_tivi = models.BooleanField(
        'Có TV',
        default=False,
        help_text='Phòng có tivi'
    )
    
    co_ban_cong = models.BooleanField(
        'Có ban công',
        default=False,
        help_text='Phòng có ban công'
    )
    
    co_phong_tam_rieng = models.BooleanField(
        'Có phòng tắm riêng',
        default=True,
        help_text='Phòng có phòng tắm riêng'
    )
    
    thiet_bi_y_te = models.TextField(
        'Thiết bị y tế',
        blank=True,
        null=True,
        help_text='Danh sách thiết bị y tế trong phòng'
    )
    
    # Pricing
    gia_phong_ngay = models.DecimalField(
        'Giá phòng/ngày',
        max_digits=12,
        decimal_places=0,
        help_text='Giá phòng theo ngày (VND)'
    )
    
    gia_phong_gio = models.DecimalField(
        'Giá phòng/giờ',
        max_digits=12,
        decimal_places=0,
        blank=True,
        null=True,
        help_text='Giá phòng theo giờ (VND)'
    )
    
    # Status and Availability
    trang_thai = models.CharField(
        'Trạng thái',
        max_length=20,
        choices=TRANG_THAI_CHOICES,
        default='Available',
        help_text='Trạng thái hiện tại của phòng'
    )
    
    ghi_chu = models.TextField(
        'Ghi chú',
        blank=True,
        null=True,
        help_text='Ghi chú về tình trạng phòng'
    )
    
    # Maintenance
    ngay_bao_tri_cuoi = models.DateTimeField(
        'Ngày bảo trì cuối',
        blank=True,
        null=True,
        help_text='Ngày bảo trì lần cuối'
    )
    
    ngay_bao_tri_tiep_theo = models.DateTimeField(
        'Ngày bảo trì tiếp theo',
        blank=True,
        null=True,
        help_text='Ngày bảo trì dự kiến tiếp theo'
    )
    
    # Timestamps
    ngay_tao = models.DateTimeField(
        'Ngày tạo',
        default=timezone.now,
        help_text='Ngày tạo bản ghi phòng'
    )
    
    ngay_cap_nhat = models.DateTimeField(
        'Ngày cập nhật',
        auto_now=True,
        help_text='Ngày cập nhật thông tin lần cuối'
    )
    
    class Meta:
        db_table = 'PhongBenh'
        verbose_name = 'Phòng bệnh'
        verbose_name_plural = 'Phòng bệnh'
        ordering = ['so_phong']
        indexes = [
            models.Index(fields=['so_phong']),
            models.Index(fields=['loai_phong']),
            models.Index(fields=['trang_thai']),
            models.Index(fields=['khoa_phong', 'loai_phong']),
            models.Index(fields=['tang', 'toa_nha']),
            models.Index(fields=['trang_thai', 'loai_phong']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(so_giuong__gt=0),
                name='check_positive_bed_count'
            ),
            models.CheckConstraint(
                check=models.Q(dien_tich__gt=0),
                name='check_positive_area'
            ),
            models.CheckConstraint(
                check=models.Q(gia_phong_ngay__gt=0),
                name='check_positive_daily_rate'
            ),
            models.CheckConstraint(
                check=models.Q(tang__gte=0),
                name='check_non_negative_floor'
            ),
        ]
    
    def __str__(self):
        department_name = self.khoa_phong.ten_khoa_phong if self.khoa_phong else 'N/A'
        return f"Phòng {self.so_phong} - {self.get_loai_phong_display()} ({department_name})"
    
    def save(self, *args, **kwargs):
        """Override save to validate and update timestamps."""
        self.full_clean()
        super().save(*args, **kwargs)
    
    def clean(self):
        """Validate model fields."""
        # Validate room pricing
        if self.gia_phong_ngay <= 0:
            raise ValidationError({
                'gia_phong_ngay': 'Giá phòng phải lớn hơn 0'
            })
        
        if self.gia_phong_gio and self.gia_phong_gio <= 0:
            raise ValidationError({
                'gia_phong_gio': 'Giá phòng theo giờ phải lớn hơn 0'
            })
        
        # Validate maintenance dates
        if (self.ngay_bao_tri_cuoi and self.ngay_bao_tri_tiep_theo and 
            self.ngay_bao_tri_tiep_theo <= self.ngay_bao_tri_cuoi):
            raise ValidationError({
                'ngay_bao_tri_tiep_theo': 'Ngày bảo trì tiếp theo phải sau ngày bảo trì cuối'
            })
        
        # Validate capacity based on room type
        if self.loai_phong == 'ICU' and self.so_giuong > 1:
            raise ValidationError({
                'so_giuong': 'Phòng ICU chỉ nên có 1 giường'
            })
        
        if self.loai_phong == 'Phau_Thuat' and self.so_giuong > 2:
            raise ValidationError({
                'so_giuong': 'Phòng phẫu thuật không nên có quá 2 giường'
            })
    
    # Business Methods
    def is_available(self) -> bool:
        """Check if room is available for booking."""
        return self.trang_thai == 'Available'
    
    def is_occupied(self) -> bool:
        """Check if room is currently occupied."""
        return self.trang_thai == 'Occupied'
    
    def is_operational(self) -> bool:
        """Check if room is operational (not maintenance or out of order)."""
        return self.trang_thai not in ['Maintenance', 'Out_Of_Order']
    
    def can_accommodate_patients(self, patient_count: int) -> bool:
        """Check if room can accommodate specified number of patients."""
        return self.is_available() and patient_count <= self.so_giuong
    
    def is_premium_room(self) -> bool:
        """Check if room is premium (VIP or special type)."""
        return self.loai_phong in ['VIP', 'ICU']
    
    def is_private_room(self) -> bool:
        """Check if room is private (single bed)."""
        return self.so_giuong == 1
    
    def requires_special_equipment(self) -> bool:
        """Check if room type requires special medical equipment."""
        return self.loai_phong in ['ICU', 'Phau_Thuat', 'Cap_Cuu']
    
    def get_amenities_list(self) -> list:
        """Get list of room amenities."""
        amenities = []
        
        if self.co_dieu_hoa:
            amenities.append('Điều hòa')
        if self.co_tivi:
            amenities.append('TV')
        if self.co_ban_cong:
            amenities.append('Ban công')
        if self.co_phong_tam_rieng:
            amenities.append('Phòng tắm riêng')
        
        return amenities
    
    def get_medical_equipment_list(self) -> list:
        """Get list of medical equipment in room."""
        if not self.thiet_bi_y_te:
            return []
        
        return [equipment.strip() for equipment in self.thiet_bi_y_te.split('\n') if equipment.strip()]
    
    def calculate_daily_cost(self, days: int) -> Decimal:
        """Calculate total cost for specified number of days."""
        return self.gia_phong_ngay * days
    
    def calculate_hourly_cost(self, hours: int) -> Decimal:
        """Calculate total cost for specified number of hours."""
        if not self.gia_phong_gio:
            # If no hourly rate, calculate based on daily rate
            hourly_rate = self.gia_phong_ngay / 24
        else:
            hourly_rate = self.gia_phong_gio
        
        return hourly_rate * hours
    
    def get_location_display(self) -> str:
        """Get formatted location display."""
        return f"{self.toa_nha}, Tầng {self.tang}"
    
    def get_capacity_display(self) -> str:
        """Get formatted capacity display."""
        return f"{self.so_giuong} giường, {self.dien_tich} m²"
    
    def occupy_room(self, patient_id: int = None, notes: str = None):
        """Mark room as occupied."""
        if not self.is_available():
            raise ValueError(f"Phòng {self.so_phong} không thể sử dụng được")
        
        old_status = self.trang_thai
        self.trang_thai = 'Occupied'
        if notes:
            self.ghi_chu = notes
        
        self.save(update_fields=['trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_room_occupied_event(patient_id)
    
    def free_room(self, notes: str = None):
        """Mark room as available after cleaning."""
        self.trang_thai = 'Cleaning'
        if notes:
            self.ghi_chu = notes
        
        self.save(update_fields=['trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_room_freed_event()
    
    def complete_cleaning(self):
        """Mark room as available after cleaning is complete."""
        if self.trang_thai != 'Cleaning':
            raise ValueError("Phòng không trong trạng thái dọn dẹp")
        
        self.trang_thai = 'Available'
        self.ghi_chu = 'Đã dọn dẹp xong'
        self.save(update_fields=['trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_room_available_event()
    
    def reserve_room(self, reservation_notes: str = None):
        """Reserve room for future use."""
        if not self.is_available():
            raise ValueError(f"Phòng {self.so_phong} không thể đặt trước")
        
        old_status = self.trang_thai
        self.trang_thai = 'Reserved'
        if reservation_notes:
            self.ghi_chu = reservation_notes
        
        self.save(update_fields=['trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_room_reserved_event()
    
    def cancel_reservation(self):
        """Cancel room reservation."""
        if self.trang_thai != 'Reserved':
            raise ValueError("Phòng không trong trạng thái đặt trước")
        
        self.trang_thai = 'Available'
        self.ghi_chu = 'Đã hủy đặt trước'
        self.save(update_fields=['trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_room_reservation_cancelled_event()
    
    def set_maintenance_mode(self, maintenance_notes: str = None, 
                           expected_completion: timezone.datetime = None):
        """Set room to maintenance mode."""
        old_status = self.trang_thai
        self.trang_thai = 'Maintenance'
        self.ngay_bao_tri_cuoi = timezone.now()
        
        if maintenance_notes:
            self.ghi_chu = maintenance_notes
        if expected_completion:
            self.ngay_bao_tri_tiep_theo = expected_completion
        
        self.save(update_fields=[
            'trang_thai', 'ghi_chu', 'ngay_bao_tri_cuoi', 
            'ngay_bao_tri_tiep_theo', 'ngay_cap_nhat'
        ])
        
        # Raise domain event
        self.raise_room_maintenance_started_event(maintenance_notes)
    
    def complete_maintenance(self):
        """Complete room maintenance."""
        if self.trang_thai != 'Maintenance':
            raise ValueError("Phòng không trong trạng thái bảo trì")
        
        self.trang_thai = 'Available'
        self.ghi_chu = 'Đã hoàn thành bảo trì'
        
        # Schedule next maintenance (6 months from now)
        from datetime import timedelta
        self.ngay_bao_tri_tiep_theo = timezone.now() + timedelta(days=180)
        
        self.save(update_fields=[
            'trang_thai', 'ghi_chu', 'ngay_bao_tri_tiep_theo', 'ngay_cap_nhat'
        ])
        
        # Raise domain event
        self.raise_room_maintenance_completed_event()
    
    def set_out_of_order(self, reason: str):
        """Set room as out of order."""
        old_status = self.trang_thai
        self.trang_thai = 'Out_Of_Order'
        self.ghi_chu = f"Hỏng hóc: {reason}"
        
        self.save(update_fields=['trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_room_out_of_order_event(reason)
    
    def repair_room(self):
        """Repair room and make it available."""
        if self.trang_thai != 'Out_Of_Order':
            raise ValueError("Phòng không trong trạng thái hỏng hóc")
        
        self.trang_thai = 'Available'
        self.ghi_chu = 'Đã sửa chữa xong'
        
        self.save(update_fields=['trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_room_repaired_event()
    
    def update_pricing(self, daily_rate: Decimal, hourly_rate: Decimal = None):
        """Update room pricing."""
        old_daily_rate = self.gia_phong_ngay
        old_hourly_rate = self.gia_phong_gio
        
        self.gia_phong_ngay = daily_rate
        if hourly_rate is not None:
            self.gia_phong_gio = hourly_rate
        
        self.save(update_fields=['gia_phong_ngay', 'gia_phong_gio', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_room_pricing_updated_event(old_daily_rate, daily_rate, old_hourly_rate, hourly_rate)
    
    def add_medical_equipment(self, equipment: str):
        """Add medical equipment to room."""
        current_equipment = self.get_medical_equipment_list()
        if equipment not in current_equipment:
            current_equipment.append(equipment)
            self.thiet_bi_y_te = '\n'.join(current_equipment)
            self.save(update_fields=['thiet_bi_y_te', 'ngay_cap_nhat'])
    
    def remove_medical_equipment(self, equipment: str):
        """Remove medical equipment from room."""
        current_equipment = self.get_medical_equipment_list()
        if equipment in current_equipment:
            current_equipment.remove(equipment)
            self.thiet_bi_y_te = '\n'.join(current_equipment)
            self.save(update_fields=['thiet_bi_y_te', 'ngay_cap_nhat'])
    
    @property
    def is_vip_room(self) -> bool:
        """Check if room is VIP."""
        return self.loai_phong == 'VIP'
    
    @property
    def is_icu_room(self) -> bool:
        """Check if room is ICU."""
        return self.loai_phong == 'ICU'
    
    @property
    def is_surgery_room(self) -> bool:
        """Check if room is surgery room."""
        return self.loai_phong == 'Phau_Thuat'
    
    @property
    def needs_maintenance(self) -> bool:
        """Check if room needs maintenance."""
        if not self.ngay_bao_tri_tiep_theo:
            return False
        return timezone.now() >= self.ngay_bao_tri_tiep_theo
    
    @property
    def occupancy_status(self) -> dict:
        """Get detailed occupancy status."""
        return {
            'status': self.get_trang_thai_display(),
            'is_available': self.is_available(),
            'is_occupied': self.is_occupied(),
            'is_operational': self.is_operational(),
            'bed_count': self.so_giuong,
            'notes': self.ghi_chu
        }
    
    @property
    def room_features(self) -> dict:
        """Get room features summary."""
        return {
            'type': self.get_loai_phong_display(),
            'capacity': f"{self.so_giuong} giường",
            'area': f"{self.dien_tich} m²",
            'amenities': self.get_amenities_list(),
            'medical_equipment': self.get_medical_equipment_list(),
            'location': self.get_location_display(),
            'pricing': {
                'daily': float(self.gia_phong_ngay),
                'hourly': float(self.gia_phong_gio) if self.gia_phong_gio else None
            }
        }
    
    # Domain Events
    def raise_room_created_event(self):
        """Raise room created domain event."""
        from apps.domain.events.domain_events import RoomCreatedEvent
        event = RoomCreatedEvent(
            room_id=self.PhongID,
            room_number=self.so_phong,
            room_type=self.loai_phong,
            department_id=self.khoa_phong.KhoaPhongID,
            bed_count=self.so_giuong,
            daily_rate=float(self.gia_phong_ngay),
            created_at=self.ngay_tao
        )
        self.add_domain_event(event)
    
    def raise_room_occupied_event(self, patient_id: int = None):
        """Raise room occupied domain event."""
        from apps.domain.events.domain_events import RoomOccupiedEvent
        event = RoomOccupiedEvent(
            room_id=self.PhongID,
            room_number=self.so_phong,
            patient_id=patient_id,
            occupied_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_room_freed_event(self):
        """Raise room freed domain event."""
        from apps.domain.events.domain_events import RoomFreedEvent
        event = RoomFreedEvent(
            room_id=self.PhongID,
            room_number=self.so_phong,
            freed_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_room_available_event(self):
        """Raise room available domain event."""
        from apps.domain.events.domain_events import RoomAvailableEvent
        event = RoomAvailableEvent(
            room_id=self.PhongID,
            room_number=self.so_phong,
            room_type=self.loai_phong,
            bed_count=self.so_giuong,
            available_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_room_reserved_event(self):
        """Raise room reserved domain event."""
        from apps.domain.events.domain_events import RoomReservedEvent
        event = RoomReservedEvent(
            room_id=self.PhongID,
            room_number=self.so_phong,
            reserved_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_room_reservation_cancelled_event(self):
        """Raise room reservation cancelled domain event."""
        from apps.domain.events.domain_events import RoomReservationCancelledEvent
        event = RoomReservationCancelledEvent(
            room_id=self.PhongID,
            room_number=self.so_phong,
            cancelled_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_room_maintenance_started_event(self, maintenance_notes: str = None):
        """Raise room maintenance started domain event."""
        from apps.domain.events.domain_events import RoomMaintenanceStartedEvent
        event = RoomMaintenanceStartedEvent(
            room_id=self.PhongID,
            room_number=self.so_phong,
            maintenance_notes=maintenance_notes,
            started_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_room_maintenance_completed_event(self):
        """Raise room maintenance completed domain event."""
        from apps.domain.events.domain_events import RoomMaintenanceCompletedEvent
        event = RoomMaintenanceCompletedEvent(
            room_id=self.PhongID,
            room_number=self.so_phong,
            completed_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_room_out_of_order_event(self, reason: str):
        """Raise room out of order domain event."""
        from apps.domain.events.domain_events import RoomOutOfOrderEvent
        event = RoomOutOfOrderEvent(
            room_id=self.PhongID,
            room_number=self.so_phong,
            reason=reason,
            reported_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_room_repaired_event(self):
        """Raise room repaired domain event."""
        from apps.domain.events.domain_events import RoomRepairedEvent
        event = RoomRepairedEvent(
            room_id=self.PhongID,
            room_number=self.so_phong,
            repaired_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_room_pricing_updated_event(self, old_daily_rate: Decimal, new_daily_rate: Decimal,
                                       old_hourly_rate: Decimal = None, new_hourly_rate: Decimal = None):
        """Raise room pricing updated domain event."""
        from apps.domain.events.domain_events import RoomPricingUpdatedEvent
        event = RoomPricingUpdatedEvent(
            room_id=self.PhongID,
            room_number=self.so_phong,
            old_daily_rate=float(old_daily_rate),
            new_daily_rate=float(new_daily_rate),
            old_hourly_rate=float(old_hourly_rate) if old_hourly_rate else None,
            new_hourly_rate=float(new_hourly_rate) if new_hourly_rate else None,
            updated_at=timezone.now()
        )
        self.add_domain_event(event)
