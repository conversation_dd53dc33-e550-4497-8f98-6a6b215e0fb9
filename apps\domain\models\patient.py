"""
Patient Domain Model
Defines the Patient entity for hospital management system.
"""
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.domain.shared.base_entity import BaseEntity
from datetime import date
import re


class <PERSON><PERSON><PERSON><PERSON>(models.Model, BaseEntity):
    """
    Patient model for hospital management system.
    Represents patients receiving medical care.
    """
    
    GIOI_TINH_CHOICES = [
        ('Nam', 'Nam'),
        ('Nữ', 'Nữ'),
        ('<PERSON>h<PERSON><PERSON>', '<PERSON>h<PERSON><PERSON>'),
    ]
    
    TRANG_THAI_CHOICES = [
        ('Active', 'Đang điều trị'),
        ('Discharged', 'Đã xuất viện'),
        ('Transferred', 'Chuyển viện'),
        ('Deceased', 'Đã mất'),
        ('Inactive', 'Không hoạt động'),
    ]
    
    LOAI_BAO_HIEM_CHOICES = [
        ('BHYT', '<PERSON><PERSON><PERSON> hiểm y tế'),
        ('BHTN', '<PERSON><PERSON><PERSON> hiểm tư nhân'),
        ('None', '<PERSON>hông có bảo hiểm'),
    ]
    
    # Primary Key
    BenhNhanID = models.AutoField(primary_key=True)
    
    # Personal Information
    ho_ten = models.CharField(
        'Họ và tên',
        max_length=100,
        help_text='Họ và tên đầy đủ của bệnh nhân'
    )
    
    ngay_sinh = models.DateField(
        'Ngày sinh',
        help_text='Ngày sinh của bệnh nhân'
    )
    
    gioi_tinh = models.CharField(
        'Giới tính',
        max_length=10,
        choices=GIOI_TINH_CHOICES,
        help_text='Giới tính của bệnh nhân'
    )
    
    # Identification
    cmnd = models.CharField(
        'CMND/CCCD',
        max_length=20,
        unique=True,
        help_text='Số chứng minh nhân dân hoặc căn cước công dân'
    )
    
    # Contact Information
    dia_chi = models.TextField(
        'Địa chỉ',
        help_text='Địa chỉ thường trú của bệnh nhân'
    )
    
    so_dien_thoai = models.CharField(
        'Số điện thoại',
        max_length=15,
        help_text='Số điện thoại liên lạc'
    )
    
    email = models.EmailField(
        'Email',
        blank=True,
        null=True,
        help_text='Địa chỉ email của bệnh nhân'
    )
    
    # Emergency Contact
    nguoi_lien_he_khan_cap = models.CharField(
        'Người liên hệ khẩn cấp',
        max_length=100,
        help_text='Tên người liên hệ khi có tình huống khẩn cấp'
    )
    
    sdt_nguoi_lien_he = models.CharField(
        'SĐT người liên hệ',
        max_length=15,
        help_text='Số điện thoại người liên hệ khẩn cấp'
    )
    
    moi_quan_he = models.CharField(
        'Mối quan hệ',
        max_length=50,
        help_text='Mối quan hệ với người liên hệ khẩn cấp'
    )
    
    # Insurance Information
    loai_bao_hiem = models.CharField(
        'Loại bảo hiểm',
        max_length=20,
        choices=LOAI_BAO_HIEM_CHOICES,
        default='None',
        help_text='Loại bảo hiểm y tế'
    )
    
    so_the_bao_hiem = models.CharField(
        'Số thẻ bảo hiểm',
        max_length=30,
        blank=True,
        null=True,
        help_text='Số thẻ bảo hiểm y tế'
    )
    
    noi_dang_ky_kham = models.CharField(
        'Nơi đăng ký khám',
        max_length=200,
        blank=True,
        null=True,
        help_text='Nơi đăng ký khám chữa bệnh ban đầu'
    )
    
    # Medical Information
    nhom_mau = models.CharField(
        'Nhóm máu',
        max_length=5,
        blank=True,
        null=True,
        choices=[
            ('A', 'A'),
            ('B', 'B'),
            ('AB', 'AB'),
            ('O', 'O'),
        ],
        help_text='Nhóm máu của bệnh nhân'
    )
    
    yeu_to_rh = models.CharField(
        'Yếu tố Rh',
        max_length=10,
        blank=True,
        null=True,
        choices=[
            ('+', 'Dương tính'),
            ('-', 'Âm tính'),
        ],
        help_text='Yếu tố Rh của nhóm máu'
    )
    
    di_ung_thuoc = models.TextField(
        'Dị ứng thuốc',
        blank=True,
        null=True,
        help_text='Danh sách các loại thuốc gây dị ứng'
    )
    
    benh_ly_nền = models.TextField(
        'Bệnh lý nền',
        blank=True,
        null=True,
        help_text='Các bệnh lý mãn tính hoặc tiền sử bệnh'
    )
    
    # Status and Tracking
    trang_thai = models.CharField(
        'Trạng thái',
        max_length=20,
        choices=TRANG_THAI_CHOICES,
        default='Active',
        help_text='Trạng thái hiện tại của bệnh nhân'
    )
    
    ma_benh_nhan = models.CharField(
        'Mã bệnh nhân',
        max_length=20,
        unique=True,
        help_text='Mã định danh duy nhất của bệnh nhân'
    )
    
    # Timestamps
    ngay_dang_ky = models.DateTimeField(
        'Ngày đăng ký',
        default=timezone.now,
        help_text='Ngày đăng ký làm bệnh nhân'
    )
    
    lan_kham_cuoi = models.DateTimeField(
        'Lần khám cuối',
        blank=True,
        null=True,
        help_text='Thời gian khám bệnh lần cuối'
    )
    
    ngay_cap_nhat = models.DateTimeField(
        'Ngày cập nhật',
        auto_now=True,
        help_text='Ngày cập nhật thông tin lần cuối'
    )
    
    class Meta:
        db_table = 'BenhNhan'
        verbose_name = 'Bệnh nhân'
        verbose_name_plural = 'Bệnh nhân'
        ordering = ['-ngay_dang_ky']
        indexes = [
            models.Index(fields=['cmnd']),
            models.Index(fields=['ma_benh_nhan']),
            models.Index(fields=['ho_ten']),
            models.Index(fields=['so_dien_thoai']),
            models.Index(fields=['email']),
            models.Index(fields=['trang_thai']),
            models.Index(fields=['ngay_sinh']),
            models.Index(fields=['so_the_bao_hiem']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(ngay_sinh__lte=timezone.now().date()),
                name='check_birth_date_not_future'
            ),
        ]
    
    def __str__(self):
        return f"{self.ho_ten} ({self.ma_benh_nhan})"
    
    def save(self, *args, **kwargs):
        """Override save to generate patient code and validate data."""
        # Auto-generate patient code if not provided
        if not self.ma_benh_nhan:
            self.ma_benh_nhan = self.generate_patient_code()
        
        self.full_clean()
        super().save(*args, **kwargs)
    
    def clean(self):
        """Validate model fields."""
        # Validate birth date
        if self.ngay_sinh and self.ngay_sinh > date.today():
            raise ValidationError({
                'ngay_sinh': 'Ngày sinh không thể ở tương lai'
            })
        
        # Validate age (must be reasonable)
        if self.ngay_sinh:
            age = self.get_age()
            if age > 150:
                raise ValidationError({
                    'ngay_sinh': 'Tuổi không hợp lệ'
                })
        
        # Validate CMND format
        if self.cmnd:
            if not re.match(r'^[0-9]{9,12}$', self.cmnd):
                raise ValidationError({
                    'cmnd': 'CMND/CCCD phải có 9-12 chữ số'
                })
        
        # Validate phone number format
        if self.so_dien_thoai:
            phone_pattern = r'^[\d\-\+\(\)\s]+$'
            if not re.match(phone_pattern, self.so_dien_thoai):
                raise ValidationError({
                    'so_dien_thoai': 'Số điện thoại không hợp lệ'
                })
        
        # Validate emergency contact phone
        if self.sdt_nguoi_lien_he:
            if not re.match(phone_pattern, self.sdt_nguoi_lien_he):
                raise ValidationError({
                    'sdt_nguoi_lien_he': 'Số điện thoại người liên hệ không hợp lệ'
                })
        
        # Validate insurance number if insurance type is provided
        if self.loai_bao_hiem != 'None' and not self.so_the_bao_hiem:
            raise ValidationError({
                'so_the_bao_hiem': 'Số thẻ bảo hiểm là bắt buộc khi có bảo hiểm'
            })
    
    def generate_patient_code(self) -> str:
        """Generate unique patient code."""
        from datetime import datetime
        
        # Format: BN + YYYYMMDD + sequential number
        today = datetime.now()
        date_str = today.strftime('%Y%m%d')
        prefix = f"BN{date_str}"
        
        # Find the highest sequential number for today
        existing_codes = BenhNhan.objects.filter(
            ma_benh_nhan__startswith=prefix
        ).values_list('ma_benh_nhan', flat=True)
        
        max_seq = 0
        for code in existing_codes:
            try:
                seq = int(code[len(prefix):])
                max_seq = max(max_seq, seq)
            except (ValueError, IndexError):
                continue
        
        return f"{prefix}{max_seq + 1:04d}"
    
    # Business Methods
    def get_age(self) -> int:
        """Calculate patient's age in years."""
        if not self.ngay_sinh:
            return 0
        
        today = date.today()
        age = today.year - self.ngay_sinh.year
        
        # Adjust if birthday hasn't occurred this year
        if today.month < self.ngay_sinh.month or \
           (today.month == self.ngay_sinh.month and today.day < self.ngay_sinh.day):
            age -= 1
        
        return age
    
    def is_adult(self) -> bool:
        """Check if patient is an adult (18+ years)."""
        return self.get_age() >= 18
    
    def is_child(self) -> bool:
        """Check if patient is a child (under 18 years)."""
        return self.get_age() < 18
    
    def is_elderly(self) -> bool:
        """Check if patient is elderly (65+ years)."""
        return self.get_age() >= 65
    
    def has_insurance(self) -> bool:
        """Check if patient has insurance coverage."""
        return self.loai_bao_hiem != 'None' and self.so_the_bao_hiem
    
    def is_active_patient(self) -> bool:
        """Check if patient is currently active."""
        return self.trang_thai == 'Active'
    
    def get_full_blood_type(self) -> str:
        """Get complete blood type including Rh factor."""
        if not self.nhom_mau:
            return 'Chưa xác định'
        
        blood_type = self.nhom_mau
        if self.yeu_to_rh:
            blood_type += self.yeu_to_rh
        
        return blood_type
    
    def has_allergies(self) -> bool:
        """Check if patient has known drug allergies."""
        return bool(self.di_ung_thuoc and self.di_ung_thuoc.strip())
    
    def has_chronic_conditions(self) -> bool:
        """Check if patient has chronic medical conditions."""
        return bool(self.benh_ly_nen and self.benh_ly_nen.strip())
    
    def get_contact_info(self) -> dict:
        """Get patient contact information."""
        return {
            'address': self.dia_chi,
            'phone': self.so_dien_thoai,
            'email': self.email,
            'emergency_contact': {
                'name': self.nguoi_lien_he_khan_cap,
                'phone': self.sdt_nguoi_lien_he,
                'relationship': self.moi_quan_he
            }
        }
    
    def get_insurance_info(self) -> dict:
        """Get patient insurance information."""
        return {
            'type': self.get_loai_bao_hiem_display(),
            'number': self.so_the_bao_hiem,
            'registered_facility': self.noi_dang_ky_kham,
            'has_coverage': self.has_insurance()
        }
    
    def get_medical_profile(self) -> dict:
        """Get patient medical profile summary."""
        return {
            'age': self.get_age(),
            'gender': self.get_gioi_tinh_display(),
            'blood_type': self.get_full_blood_type(),
            'allergies': self.di_ung_thuoc if self.has_allergies() else 'Không có',
            'chronic_conditions': self.benh_ly_nen if self.has_chronic_conditions() else 'Không có',
            'has_allergies': self.has_allergies(),
            'has_chronic_conditions': self.has_chronic_conditions(),
            'last_visit': self.lan_kham_cuoi
        }
    
    def update_last_visit(self):
        """Update last visit timestamp."""
        self.lan_kham_cuoi = timezone.now()
        self.save(update_fields=['lan_kham_cuoi', 'ngay_cap_nhat'])
    
    def discharge_patient(self, discharge_reason: str = None):
        """Discharge patient from hospital."""
        old_status = self.trang_thai
        self.trang_thai = 'Discharged'
        self.save(update_fields=['trang_thai', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_patient_discharged_event(discharge_reason)
    
    def transfer_patient(self, destination: str):
        """Transfer patient to another facility."""
        old_status = self.trang_thai
        self.trang_thai = 'Transferred'
        self.save(update_fields=['trang_thai', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_patient_transferred_event(destination)
    
    def reactivate_patient(self):
        """Reactivate patient for treatment."""
        old_status = self.trang_thai
        self.trang_thai = 'Active'
        self.save(update_fields=['trang_thai', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_patient_status_changed_event(old_status, 'Active')
    
    def update_insurance_info(self, insurance_type: str, insurance_number: str = None, 
                            registered_facility: str = None):
        """Update patient insurance information."""
        self.loai_bao_hiem = insurance_type
        if insurance_number:
            self.so_the_bao_hiem = insurance_number
        if registered_facility:
            self.noi_dang_ky_kham = registered_facility
        
        self.save(update_fields=[
            'loai_bao_hiem', 'so_the_bao_hiem', 'noi_dang_ky_kham', 'ngay_cap_nhat'
        ])
    
    def update_medical_info(self, blood_group: str = None, rh_factor: str = None,
                          allergies: str = None, chronic_conditions: str = None):
        """Update patient medical information."""
        updated_fields = ['ngay_cap_nhat']
        
        if blood_group is not None:
            self.nhom_mau = blood_group
            updated_fields.append('nhom_mau')
        
        if rh_factor is not None:
            self.yeu_to_rh = rh_factor
            updated_fields.append('yeu_to_rh')
        
        if allergies is not None:
            self.di_ung_thuoc = allergies
            updated_fields.append('di_ung_thuoc')
        
        if chronic_conditions is not None:
            self.benh_ly_nen = chronic_conditions
            updated_fields.append('benh_ly_nen')
        
        self.save(update_fields=updated_fields)
    
    @property
    def display_name(self) -> str:
        """Get display name with patient code."""
        return f"{self.ho_ten} ({self.ma_benh_nhan})"
    
    @property
    def age_group(self) -> str:
        """Get age group classification."""
        age = self.get_age()
        if age < 2:
            return 'Sơ sinh'
        elif age < 12:
            return 'Trẻ em'
        elif age < 18:
            return 'Thiếu niên'
        elif age < 65:
            return 'Người lớn'
        else:
            return 'Người cao tuổi'
    
    @property
    def insurance_coverage_display(self) -> str:
        """Get insurance coverage display text."""
        if self.has_insurance():
            return f"{self.get_loai_bao_hiem_display()} - {self.so_the_bao_hiem}"
        return 'Không có bảo hiểm'
    
    # Domain Events
    def raise_patient_registered_event(self):
        """Raise patient registered domain event."""
        from apps.domain.events.domain_events import PatientRegisteredEvent
        event = PatientRegisteredEvent(
            patient_id=self.BenhNhanID,
            patient_code=self.ma_benh_nhan,
            patient_name=self.ho_ten,
            birth_date=self.ngay_sinh,
            gender=self.gioi_tinh,
            cmnd=self.cmnd,
            registered_at=self.ngay_dang_ky
        )
        self.add_domain_event(event)
    
    def raise_patient_discharged_event(self, reason: str = None):
        """Raise patient discharged domain event."""
        from apps.domain.events.domain_events import PatientDischargedEvent
        event = PatientDischargedEvent(
            patient_id=self.BenhNhanID,
            patient_code=self.ma_benh_nhan,
            patient_name=self.ho_ten,
            discharge_reason=reason,
            discharged_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_patient_transferred_event(self, destination: str):
        """Raise patient transferred domain event."""
        from apps.domain.events.domain_events import PatientTransferredEvent
        event = PatientTransferredEvent(
            patient_id=self.BenhNhanID,
            patient_code=self.ma_benh_nhan,
            patient_name=self.ho_ten,
            destination=destination,
            transferred_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_patient_status_changed_event(self, old_status: str, new_status: str):
        """Raise patient status changed domain event."""
        from apps.domain.events.domain_events import PatientStatusChangedEvent
        event = PatientStatusChangedEvent(
            patient_id=self.BenhNhanID,
            patient_code=self.ma_benh_nhan,
            patient_name=self.ho_ten,
            old_status=old_status,
            new_status=new_status,
            changed_at=timezone.now()
        )
        self.add_domain_event(event)
