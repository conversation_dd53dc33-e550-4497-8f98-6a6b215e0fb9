# Generated by Django 3.2.3 on 2021-05-27 17:46

from pathlib import Path

from django.db import migrations, models

parent_dir = Path(__file__).resolve(strict=True).parent


class Migration(migrations.Migration):
    dependencies = [
        ("token_blacklist", "0008_migrate_to_bigautofield"),
    ]

    operations = [
        migrations.AlterField(
            model_name="blacklistedtoken",
            name="id",
            field=models.BigAutoField(primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name="outstandingtoken",
            name="id",
            field=models.BigAutoField(primary_key=True, serialize=False),
        ),
    ]
